<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>英语单词记忆游戏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            width: 100%;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 30px;
            text-align: center;
        }

        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .game-modes {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .mode-btn {
            padding: 15px 25px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .mode-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .mode-btn.active {
            background: linear-gradient(45deg, #2ecc71, #27ae60);
        }

        .game-area {
            min-height: 400px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .word-card {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 40px;
            border-radius: 20px;
            font-size: 2em;
            font-weight: bold;
            margin: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            min-width: 300px;
        }

        .word-card:hover {
            transform: scale(1.05);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }

        .word-card.flipped {
            background: linear-gradient(135deg, #fd79a8, #e84393);
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .option-btn {
            padding: 15px;
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .option-btn:hover {
            background: #e9ecef;
            border-color: #74b9ff;
        }

        .option-btn.correct {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }

        .option-btn.wrong {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }

        .score {
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: #e9ecef;
            border-radius: 5px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #2ecc71, #27ae60);
            transition: width 0.3s ease;
        }

        .celebration {
            font-size: 3em;
            animation: bounce 1s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-30px); }
            60% { transform: translateY(-15px); }
        }

        .next-btn {
            padding: 15px 30px;
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            margin: 20px;
            transition: all 0.3s ease;
        }

        .next-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 英语单词记忆游戏</h1>
        
        <div class="game-modes">
            <button class="mode-btn active" onclick="setGameMode('flashcard')">单词卡片</button>
            <button class="mode-btn" onclick="setGameMode('quiz')">选择题</button>
            <button class="mode-btn" onclick="setGameMode('match')">配对游戏</button>
        </div>

        <div class="score">得分: <span id="score">0</span> | 进度: <span id="current">1</span>/<span id="total">14</span></div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progress"></div>
        </div>

        <div class="game-area" id="gameArea">
            <!-- 游戏内容将在这里动态生成 -->
        </div>
    </div>

    <script>
        const words = [
            { english: 'Postcard', chinese: '明信片', pronunciation: '/ˈpəʊstkɑːd/' },
            { english: 'from', chinese: '来自', pronunciation: '/frɒm/' },
            { english: 'London', chinese: '伦敦', pronunciation: '/ˈlʌndən/' },
            { english: 'underground', chinese: '地铁', pronunciation: '/ˈʌndəɡraʊnd/' },
            { english: 'metro', chinese: '地铁', pronunciation: '/ˈmetrəʊ/' },
            { english: 'bookmark', chinese: '书签', pronunciation: '/ˈbʊkmɑːk/' },
            { english: 'think', chinese: '想，思考', pronunciation: '/θɪŋk/' },
            { english: 'cut', chinese: '切，剪', pronunciation: '/kʌt/' },
            { english: 'by car', chinese: '乘汽车', pronunciation: '/baɪ kɑː/' },
            { english: 'by bus', chinese: '乘公交车', pronunciation: '/baɪ bʌs/' },
            { english: 'on foot', chinese: '步行', pronunciation: '/ɒn fʊt/' },
            { english: 'on the way', chinese: '在路上', pronunciation: '/ɒn ðə weɪ/' },
            { english: 'a postcard from London', chinese: '一张来自伦敦的明信片', pronunciation: '/ə ˈpəʊstkɑːd frɒm ˈlʌndən/' },
            { english: 'in London', chinese: '在伦敦', pronunciation: '/ɪn ˈlʌndən/' }
        ];

        let currentMode = 'flashcard';
        let currentIndex = 0;
        let score = 0;
        let isFlipped = false;

        function setGameMode(mode) {
            currentMode = mode;
            currentIndex = 0;
            score = 0;
            updateScore();
            
            // 更新按钮状态
            document.querySelectorAll('.mode-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            startGame();
        }

        function startGame() {
            switch(currentMode) {
                case 'flashcard':
                    showFlashcard();
                    break;
                case 'quiz':
                    showQuiz();
                    break;
                case 'match':
                    showMatch();
                    break;
            }
        }

        function showFlashcard() {
            const gameArea = document.getElementById('gameArea');
            const word = words[currentIndex];
            isFlipped = false;
            
            gameArea.innerHTML = `
                <div class="word-card" onclick="flipCard()">
                    <div id="cardContent">${word.english}</div>
                    <div style="font-size: 0.6em; margin-top: 10px; opacity: 0.8;">${word.pronunciation}</div>
                </div>
                <p style="margin-top: 20px; color: #666;">点击卡片查看中文意思</p>
                <button class="next-btn" onclick="nextWord()">下一个单词</button>
            `;
        }

        function flipCard() {
            const card = document.querySelector('.word-card');
            const content = document.getElementById('cardContent');
            const word = words[currentIndex];

            if (!isFlipped) {
                card.classList.add('flipped');
                content.textContent = word.chinese;
                isFlipped = true;
                score += 10;
                updateScore();
            } else {
                card.classList.remove('flipped');
                content.textContent = word.english;
                isFlipped = false;
            }
        }

        function nextWord() {
            currentIndex++;
            if (currentIndex >= words.length) {
                showCompletion();
            } else {
                startGame();
            }
        }

        function showQuiz() {
            const gameArea = document.getElementById('gameArea');
            const word = words[currentIndex];
            const wrongAnswers = getRandomWrongAnswers(word.chinese, 3);
            const allAnswers = [...wrongAnswers, word.chinese].sort(() => Math.random() - 0.5);

            gameArea.innerHTML = `
                <div class="word-card">
                    <div>${word.english}</div>
                    <div style="font-size: 0.6em; margin-top: 10px; opacity: 0.8;">${word.pronunciation}</div>
                </div>
                <p style="margin: 20px 0; color: #333; font-size: 1.2em;">选择正确的中文意思：</p>
                <div class="options">
                    ${allAnswers.map(answer =>
                        `<button class="option-btn" onclick="checkAnswer('${answer}', '${word.chinese}')">${answer}</button>`
                    ).join('')}
                </div>
            `;
        }

        function getRandomWrongAnswers(correctAnswer, count) {
            const allAnswers = words.map(w => w.chinese).filter(answer => answer !== correctAnswer);
            const shuffled = allAnswers.sort(() => Math.random() - 0.5);
            return shuffled.slice(0, count);
        }

        function checkAnswer(selected, correct) {
            const buttons = document.querySelectorAll('.option-btn');
            buttons.forEach(btn => {
                btn.disabled = true;
                if (btn.textContent === correct) {
                    btn.classList.add('correct');
                } else if (btn.textContent === selected && selected !== correct) {
                    btn.classList.add('wrong');
                }
            });

            if (selected === correct) {
                score += 20;
                setTimeout(() => {
                    showCelebration();
                    setTimeout(nextWord, 1500);
                }, 500);
            } else {
                setTimeout(nextWord, 2000);
            }
            updateScore();
        }

        function showMatch() {
            const gameArea = document.getElementById('gameArea');
            const word = words[currentIndex];
            const pairs = [
                { text: word.english, type: 'english' },
                { text: word.chinese, type: 'chinese' }
            ].sort(() => Math.random() - 0.5);

            gameArea.innerHTML = `
                <p style="margin-bottom: 20px; color: #333; font-size: 1.2em;">将英文和中文配对：</p>
                <div style="display: flex; justify-content: center; gap: 30px; flex-wrap: wrap;">
                    ${pairs.map((pair, index) =>
                        `<div class="word-card" style="font-size: 1.5em; padding: 30px; cursor: pointer;"
                              onclick="selectForMatch(${index}, '${pair.type}')" id="match-${index}">
                            ${pair.text}
                        </div>`
                    ).join('')}
                </div>
                <div id="matchResult" style="margin-top: 20px;"></div>
            `;
        }

        let selectedMatch = null;

        function selectForMatch(index, type) {
            const card = document.getElementById(`match-${index}`);

            if (selectedMatch === null) {
                selectedMatch = { index, type };
                card.style.background = 'linear-gradient(135deg, #fdcb6e, #e17055)';
            } else {
                if (selectedMatch.index === index) {
                    // 取消选择
                    selectedMatch = null;
                    card.style.background = 'linear-gradient(135deg, #74b9ff, #0984e3)';
                } else {
                    // 检查配对
                    const isCorrect = (selectedMatch.type === 'english' && type === 'chinese') ||
                                    (selectedMatch.type === 'chinese' && type === 'english');

                    if (isCorrect) {
                        score += 30;
                        document.getElementById('matchResult').innerHTML =
                            '<div class="celebration">🎉 配对成功！</div>';
                        setTimeout(() => {
                            setTimeout(nextWord, 1000);
                        }, 1000);
                    } else {
                        document.getElementById('matchResult').innerHTML =
                            '<div style="color: #e74c3c; font-size: 1.2em;">❌ 配对错误，再试一次！</div>';
                        setTimeout(() => {
                            document.getElementById('matchResult').innerHTML = '';
                            document.getElementById(`match-${selectedMatch.index}`).style.background =
                                'linear-gradient(135deg, #74b9ff, #0984e3)';
                            selectedMatch = null;
                        }, 1500);
                    }
                    updateScore();
                }
            }
        }

        function showCelebration() {
            const gameArea = document.getElementById('gameArea');
            const celebration = document.createElement('div');
            celebration.className = 'celebration';
            celebration.textContent = '🎉 太棒了！';
            gameArea.appendChild(celebration);
        }

        function showCompletion() {
            const gameArea = document.getElementById('gameArea');
            gameArea.innerHTML = `
                <div class="celebration">🏆 恭喜完成！</div>
                <div style="font-size: 1.5em; margin: 20px 0; color: #333;">
                    最终得分: ${score} 分
                </div>
                <div style="margin: 20px 0;">
                    <button class="next-btn" onclick="restartGame()">重新开始</button>
                    <button class="next-btn" onclick="reviewWords()">复习单词</button>
                </div>
            `;
        }

        function restartGame() {
            currentIndex = 0;
            score = 0;
            updateScore();
            startGame();
        }

        function reviewWords() {
            const gameArea = document.getElementById('gameArea');
            gameArea.innerHTML = `
                <h3 style="margin-bottom: 20px; color: #333;">📚 单词复习</h3>
                <div style="text-align: left; max-height: 400px; overflow-y: auto;">
                    ${words.map(word => `
                        <div style="background: #f8f9fa; margin: 10px 0; padding: 15px; border-radius: 10px; border-left: 4px solid #74b9ff;">
                            <div style="font-weight: bold; font-size: 1.2em; color: #333;">${word.english}</div>
                            <div style="color: #666; margin: 5px 0;">${word.pronunciation}</div>
                            <div style="color: #2d3436; font-size: 1.1em;">${word.chinese}</div>
                        </div>
                    `).join('')}
                </div>
                <button class="next-btn" onclick="restartGame()" style="margin-top: 20px;">重新开始游戏</button>
            `;
        }

        function updateScore() {
            document.getElementById('score').textContent = score;
            document.getElementById('current').textContent = currentIndex + 1;
            document.getElementById('total').textContent = words.length;

            const progress = ((currentIndex + 1) / words.length) * 100;
            document.getElementById('progress').style.width = progress + '%';
        }

        // 初始化游戏
        startGame();
    </script>
</body>
</html>
