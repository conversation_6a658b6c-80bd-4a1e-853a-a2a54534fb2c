<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>英语单词记忆游戏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            width: 100%;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 30px;
            text-align: center;
        }

        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .game-modes {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .mode-btn {
            padding: 15px 25px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .mode-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .mode-btn.active {
            background: linear-gradient(45deg, #2ecc71, #27ae60);
        }

        .game-area {
            min-height: 400px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .word-card {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 40px;
            border-radius: 20px;
            font-size: 2em;
            font-weight: bold;
            margin: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            min-width: 300px;
        }

        .word-card:hover {
            transform: scale(1.05);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }

        .word-card.flipped {
            background: linear-gradient(135deg, #fd79a8, #e84393);
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .option-btn {
            padding: 15px;
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .option-btn:hover {
            background: #e9ecef;
            border-color: #74b9ff;
        }

        .option-btn.correct {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }

        .option-btn.wrong {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }

        .score {
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: #e9ecef;
            border-radius: 5px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #2ecc71, #27ae60);
            transition: width 0.3s ease;
        }

        .celebration {
            font-size: 3em;
            animation: bounce 1s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-30px); }
            60% { transform: translateY(-15px); }
        }

        .next-btn {
            padding: 15px 30px;
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            margin: 20px;
            transition: all 0.3s ease;
        }

        .next-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .fill-blank-container {
            background: linear-gradient(135deg, #a29bfe, #6c5ce7);
            color: white;
            padding: 30px;
            border-radius: 20px;
            margin: 20px 0;
            font-size: 1.5em;
            line-height: 1.8;
        }

        .blank-input {
            background: white;
            border: 3px solid #74b9ff;
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 1em;
            color: #333;
            margin: 0 3px;
            text-align: center;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .blank-input.single-letter {
            width: 40px;
            min-width: 40px;
            max-width: 40px;
        }

        .blank-input.short-word {
            width: 80px;
            min-width: 80px;
            max-width: 80px;
        }

        .blank-input.medium-word {
            width: 120px;
            min-width: 120px;
            max-width: 120px;
        }

        .blank-input.long-word {
            width: 160px;
            min-width: 160px;
            max-width: 160px;
        }

        .blank-input:focus {
            outline: none;
            border-color: #0984e3;
            box-shadow: 0 0 10px rgba(116, 185, 255, 0.5);
            transform: scale(1.05);
        }

        .blank-input.correct {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }

        .blank-input.wrong {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .hint-btn {
            background: linear-gradient(45deg, #fdcb6e, #e17055);
            color: white;
            border: none;
            border-radius: 15px;
            padding: 8px 15px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }

        .hint-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .encouragement {
            font-size: 2em;
            color: #27ae60;
            font-weight: bold;
            animation: pulse 1s ease-in-out;
            margin: 20px 0;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .difficulty-selector {
            margin: 20px 0;
            display: flex;
            justify-content: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .difficulty-btn {
            padding: 8px 16px;
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .difficulty-btn.active {
            background: #74b9ff;
            color: white;
            border-color: #0984e3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 英语单词记忆游戏</h1>
        
        <div class="game-modes">
            <button class="mode-btn active" onclick="setGameMode('flashcard')">单词卡片</button>
            <button class="mode-btn" onclick="setGameMode('quiz')">选择题</button>
            <button class="mode-btn" onclick="setGameMode('match')">配对游戏</button>
            <button class="mode-btn" onclick="setGameMode('fillblank')">填空挑战</button>
        </div>

        <div class="score">得分: <span id="score">0</span> | 进度: <span id="current">1</span>/<span id="total">14</span></div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progress"></div>
        </div>

        <div class="game-area" id="gameArea">
            <!-- 游戏内容将在这里动态生成 -->
        </div>
    </div>

    <script>
        const words = [
            { english: 'Postcard', chinese: '明信片', pronunciation: '/ˈpəʊstkɑːd/' },
            { english: 'from', chinese: '来自', pronunciation: '/frɒm/' },
            { english: 'London', chinese: '伦敦', pronunciation: '/ˈlʌndən/' },
            { english: 'underground', chinese: '地铁', pronunciation: '/ˈʌndəɡraʊnd/' },
            { english: 'metro', chinese: '地铁', pronunciation: '/ˈmetrəʊ/' },
            { english: 'bookmark', chinese: '书签', pronunciation: '/ˈbʊkmɑːk/' },
            { english: 'think', chinese: '想，思考', pronunciation: '/θɪŋk/' },
            { english: 'cut', chinese: '切，剪', pronunciation: '/kʌt/' },
            { english: 'by car', chinese: '乘汽车', pronunciation: '/baɪ kɑː/' },
            { english: 'by bus', chinese: '乘公交车', pronunciation: '/baɪ bʌs/' },
            { english: 'on foot', chinese: '步行', pronunciation: '/ɒn fʊt/' },
            { english: 'on the way', chinese: '在路上', pronunciation: '/ɒn ðə weɪ/' },
            { english: 'a postcard from London', chinese: '一张来自伦敦的明信片', pronunciation: '/ə ˈpəʊstkɑːd frɒm ˈlʌndən/' },
            { english: 'in London', chinese: '在伦敦', pronunciation: '/ɪn ˈlʌndən/' }
        ];

        let currentMode = 'flashcard';
        let currentIndex = 0;
        let score = 0;
        let isFlipped = false;
        let fillBlankDifficulty = 'easy'; // easy, medium, hard
        let selectedMatch = null;

        // 音频反馈系统
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        function playSuccessSound() {
            // 播放成功音效 - 愉快的和弦
            const frequencies = [523.25, 659.25, 783.99]; // C5, E5, G5
            frequencies.forEach((freq, index) => {
                setTimeout(() => {
                    playTone(freq, 0.3, 'sine');
                }, index * 100);
            });
        }

        function playErrorSound() {
            // 播放错误音效 - 低沉的音调
            playTone(220, 0.5, 'sawtooth');
        }

        function playEncouragementSound() {
            // 播放鼓励音效 - 上升音阶
            const frequencies = [261.63, 293.66, 329.63, 349.23, 392.00, 440.00, 493.88, 523.25];
            frequencies.forEach((freq, index) => {
                setTimeout(() => {
                    playTone(freq, 0.2, 'sine');
                }, index * 80);
            });
        }

        function playTone(frequency, duration, type = 'sine') {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.value = frequency;
            oscillator.type = type;

            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + duration);
        }

        function setGameMode(mode) {
            currentMode = mode;
            currentIndex = 0;
            score = 0;
            updateScore();
            
            // 更新按钮状态
            document.querySelectorAll('.mode-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            startGame();
        }

        function startGame() {
            switch(currentMode) {
                case 'flashcard':
                    showFlashcard();
                    break;
                case 'quiz':
                    showQuiz();
                    break;
                case 'match':
                    showMatch();
                    break;
                case 'fillblank':
                    showFillBlank();
                    break;
            }
        }

        function showFlashcard() {
            const gameArea = document.getElementById('gameArea');
            const word = words[currentIndex];
            isFlipped = false;
            
            gameArea.innerHTML = `
                <div class="word-card" onclick="flipCard()">
                    <div id="cardContent">${word.english}</div>
                    <div style="font-size: 0.6em; margin-top: 10px; opacity: 0.8;">${word.pronunciation}</div>
                </div>
                <p style="margin-top: 20px; color: #666;">点击卡片查看中文意思</p>
                <button class="next-btn" onclick="nextWord()">下一个单词</button>
            `;
        }

        function flipCard() {
            const card = document.querySelector('.word-card');
            const content = document.getElementById('cardContent');
            const word = words[currentIndex];

            if (!isFlipped) {
                card.classList.add('flipped');
                content.textContent = word.chinese;
                isFlipped = true;
                score += 10;
                updateScore();
            } else {
                card.classList.remove('flipped');
                content.textContent = word.english;
                isFlipped = false;
            }
        }

        function nextWord() {
            currentIndex++;
            if (currentIndex >= words.length) {
                showCompletion();
            } else {
                startGame();
            }
        }

        function showQuiz() {
            const gameArea = document.getElementById('gameArea');
            const word = words[currentIndex];
            const wrongAnswers = getRandomWrongAnswers(word.chinese, 3);
            const allAnswers = [...wrongAnswers, word.chinese].sort(() => Math.random() - 0.5);

            gameArea.innerHTML = `
                <div class="word-card">
                    <div>${word.english}</div>
                    <div style="font-size: 0.6em; margin-top: 10px; opacity: 0.8;">${word.pronunciation}</div>
                </div>
                <p style="margin: 20px 0; color: #333; font-size: 1.2em;">选择正确的中文意思：</p>
                <div class="options">
                    ${allAnswers.map(answer =>
                        `<button class="option-btn" onclick="checkAnswer('${answer}', '${word.chinese}')">${answer}</button>`
                    ).join('')}
                </div>
            `;
        }

        function getRandomWrongAnswers(correctAnswer, count) {
            const allAnswers = words.map(w => w.chinese).filter(answer => answer !== correctAnswer);
            const shuffled = allAnswers.sort(() => Math.random() - 0.5);
            return shuffled.slice(0, count);
        }

        function checkAnswer(selected, correct) {
            const buttons = document.querySelectorAll('.option-btn');
            buttons.forEach(btn => {
                btn.disabled = true;
                if (btn.textContent === correct) {
                    btn.classList.add('correct');
                } else if (btn.textContent === selected && selected !== correct) {
                    btn.classList.add('wrong');
                }
            });

            if (selected === correct) {
                score += 20;
                setTimeout(() => {
                    showCelebration();
                    setTimeout(nextWord, 1500);
                }, 500);
            } else {
                setTimeout(nextWord, 2000);
            }
            updateScore();
        }

        function showMatch() {
            const gameArea = document.getElementById('gameArea');
            const word = words[currentIndex];
            const pairs = [
                { text: word.english, type: 'english' },
                { text: word.chinese, type: 'chinese' }
            ].sort(() => Math.random() - 0.5);

            gameArea.innerHTML = `
                <p style="margin-bottom: 20px; color: #333; font-size: 1.2em;">将英文和中文配对：</p>
                <div style="display: flex; justify-content: center; gap: 30px; flex-wrap: wrap;">
                    ${pairs.map((pair, index) =>
                        `<div class="word-card" style="font-size: 1.5em; padding: 30px; cursor: pointer;"
                              onclick="selectForMatch(${index}, '${pair.type}')" id="match-${index}">
                            ${pair.text}
                        </div>`
                    ).join('')}
                </div>
                <div id="matchResult" style="margin-top: 20px;"></div>
            `;
        }

        function showFillBlank() {
            const gameArea = document.getElementById('gameArea');
            const word = words[currentIndex];
            const blankData = createBlankVersion(word, fillBlankDifficulty);

            gameArea.innerHTML = `
                <div class="difficulty-selector">
                    <button class="difficulty-btn ${fillBlankDifficulty === 'easy' ? 'active' : ''}"
                            onclick="changeDifficulty('easy')">简单</button>
                    <button class="difficulty-btn ${fillBlankDifficulty === 'medium' ? 'active' : ''}"
                            onclick="changeDifficulty('medium')">中等</button>
                    <button class="difficulty-btn ${fillBlankDifficulty === 'hard' ? 'active' : ''}"
                            onclick="changeDifficulty('hard')">困难</button>
                </div>

                <div class="fill-blank-container">
                    <div style="margin-bottom: 15px; font-size: 0.8em; opacity: 0.9;">
                        中文提示: ${word.chinese}
                    </div>
                    <div style="margin-bottom: 15px; font-size: 0.7em; opacity: 0.8;">
                        发音: ${word.pronunciation}
                    </div>
                    <div id="blankSentence" style="font-size: 1.2em; line-height: 2;">
                        ${replaceBlanksWithInputs(blankData.sentence, blankData.answers)}
                    </div>
                </div>

                <div style="margin: 20px 0;">
                    <button class="hint-btn" onclick="showHint()">💡 提示</button>
                    <button class="next-btn" onclick="checkFillBlank()">检查答案</button>
                </div>

                <div id="fillBlankResult"></div>
            `;

            // 存储正确答案
            window.currentBlankAnswers = blankData.answers;
            window.currentHints = blankData.hints;
            window.hintsUsed = 0;
        }

        function createBlankVersion(word, difficulty) {
            const text = word.english;
            let sentence = text;
            let answers = [];
            let hints = [];

            if (difficulty === 'easy') {
                // 简单模式：只挖掉1-2个字母
                const words = text.split(' ');
                const targetWord = words[Math.floor(Math.random() * words.length)];
                const blankCount = Math.min(2, Math.floor(targetWord.length / 3));

                let blankedWord = targetWord;
                for (let i = 0; i < blankCount; i++) {
                    const pos = Math.floor(Math.random() * blankedWord.length);
                    if (blankedWord[pos] !== '_') {
                        answers.push(blankedWord[pos].toLowerCase());
                        hints.push(`第${answers.length}个空格是字母 "${blankedWord[pos].toUpperCase()}"`);
                        blankedWord = blankedWord.substring(0, pos) +
                                    `___BLANK_${answers.length - 1}___` +
                                    blankedWord.substring(pos + 1);
                    }
                }
                sentence = text.replace(targetWord, blankedWord);

            } else if (difficulty === 'medium') {
                // 中等模式：挖掉整个单词
                const words = text.split(' ');
                const targetIndex = Math.floor(Math.random() * words.length);
                const targetWord = words[targetIndex];

                answers.push(targetWord.toLowerCase());
                hints.push(`这个词的意思是"${word.chinese}"中的一部分`);
                hints.push(`这个词有${targetWord.length}个字母`);
                hints.push(`这个词的第一个字母是"${targetWord[0].toUpperCase()}"`);

                words[targetIndex] = `___BLANK_0___`;
                sentence = words.join(' ');

            } else { // hard
                // 困难模式：挖掉多个单词或整个短语
                const words = text.split(' ');
                if (words.length > 1) {
                    const blankCount = Math.min(words.length, Math.ceil(words.length / 2));
                    const indices = [];

                    while (indices.length < blankCount) {
                        const index = Math.floor(Math.random() * words.length);
                        if (!indices.includes(index)) {
                            indices.push(index);
                        }
                    }

                    indices.sort((a, b) => a - b);
                    indices.forEach((index, i) => {
                        answers.push(words[index].toLowerCase());
                        hints.push(`第${i + 1}个空格: "${words[index]}" (${words[index].length}个字母)`);
                        words[index] = `___BLANK_${i}___`;
                    });

                    sentence = words.join(' ');
                } else {
                    // 单个单词的情况，挖掉大部分字母
                    const word = text;
                    const keepCount = Math.max(1, Math.floor(word.length / 3));
                    let blankedWord = word;

                    for (let i = keepCount; i < word.length; i++) {
                        if (blankedWord[i] !== '_') {
                            answers.push(blankedWord[i].toLowerCase());
                            hints.push(`第${answers.length}个空格是字母 "${blankedWord[i].toUpperCase()}"`);
                            blankedWord = blankedWord.substring(0, i) +
                                        `___BLANK_${answers.length - 1}___` +
                                        blankedWord.substring(i + 1);
                        }
                    }
                    sentence = blankedWord;
                }
            }

            return { sentence, answers, hints };
        }

        function replaceBlanksWithInputs(sentence, answers) {
            let result = sentence;
            answers.forEach((answer, index) => {
                const placeholder = `___BLANK_${index}___`;
                const answerLength = answer.length;

                // 根据答案长度确定输入框样式和属性
                let inputClass = 'blank-input';
                let maxLength = '';
                let placeholder_text = '';

                if (answerLength === 1) {
                    inputClass += ' single-letter';
                    maxLength = 'maxlength="1"';
                    placeholder_text = '_';
                } else if (answerLength <= 4) {
                    inputClass += ' short-word';
                    placeholder_text = '_'.repeat(answerLength);
                } else if (answerLength <= 8) {
                    inputClass += ' medium-word';
                    placeholder_text = '_'.repeat(Math.min(answerLength, 6)) + '...';
                } else {
                    inputClass += ' long-word';
                    placeholder_text = '_'.repeat(8) + '...';
                }

                const inputHtml = `<input type="text" class="${inputClass}" ${maxLength} data-answer="${answer}" onkeyup="handleBlankInput(event)" placeholder="${placeholder_text}">`;
                result = result.replace(placeholder, inputHtml);
            });
            return result;
        }

        function selectForMatch(index, type) {
            const card = document.getElementById(`match-${index}`);

            if (selectedMatch === null) {
                selectedMatch = { index, type };
                card.style.background = 'linear-gradient(135deg, #fdcb6e, #e17055)';
            } else {
                if (selectedMatch.index === index) {
                    // 取消选择
                    selectedMatch = null;
                    card.style.background = 'linear-gradient(135deg, #74b9ff, #0984e3)';
                } else {
                    // 检查配对
                    const isCorrect = (selectedMatch.type === 'english' && type === 'chinese') ||
                                    (selectedMatch.type === 'chinese' && type === 'english');

                    if (isCorrect) {
                        score += 30;
                        document.getElementById('matchResult').innerHTML =
                            '<div class="celebration">🎉 配对成功！</div>';
                        setTimeout(() => {
                            setTimeout(nextWord, 1000);
                        }, 1000);
                    } else {
                        document.getElementById('matchResult').innerHTML =
                            '<div style="color: #e74c3c; font-size: 1.2em;">❌ 配对错误，再试一次！</div>';
                        setTimeout(() => {
                            document.getElementById('matchResult').innerHTML = '';
                            document.getElementById(`match-${selectedMatch.index}`).style.background =
                                'linear-gradient(135deg, #74b9ff, #0984e3)';
                            selectedMatch = null;
                        }, 1500);
                    }
                    updateScore();
                }
            }
        }

        function showCelebration() {
            const gameArea = document.getElementById('gameArea');
            const celebration = document.createElement('div');
            celebration.className = 'celebration';
            celebration.textContent = '🎉 太棒了！';
            gameArea.appendChild(celebration);
        }

        function showCompletion() {
            const gameArea = document.getElementById('gameArea');
            gameArea.innerHTML = `
                <div class="celebration">🏆 恭喜完成！</div>
                <div style="font-size: 1.5em; margin: 20px 0; color: #333;">
                    最终得分: ${score} 分
                </div>
                <div style="margin: 20px 0;">
                    <button class="next-btn" onclick="restartGame()">重新开始</button>
                    <button class="next-btn" onclick="reviewWords()">复习单词</button>
                </div>
            `;
        }

        function restartGame() {
            currentIndex = 0;
            score = 0;
            updateScore();
            startGame();
        }

        function reviewWords() {
            const gameArea = document.getElementById('gameArea');
            gameArea.innerHTML = `
                <h3 style="margin-bottom: 20px; color: #333;">📚 单词复习</h3>
                <div style="text-align: left; max-height: 400px; overflow-y: auto;">
                    ${words.map(word => `
                        <div style="background: #f8f9fa; margin: 10px 0; padding: 15px; border-radius: 10px; border-left: 4px solid #74b9ff;">
                            <div style="font-weight: bold; font-size: 1.2em; color: #333;">${word.english}</div>
                            <div style="color: #666; margin: 5px 0;">${word.pronunciation}</div>
                            <div style="color: #2d3436; font-size: 1.1em;">${word.chinese}</div>
                        </div>
                    `).join('')}
                </div>
                <button class="next-btn" onclick="restartGame()" style="margin-top: 20px;">重新开始游戏</button>
            `;
        }

        function updateScore() {
            document.getElementById('score').textContent = score;
            document.getElementById('current').textContent = currentIndex + 1;
            document.getElementById('total').textContent = words.length;

            const progress = ((currentIndex + 1) / words.length) * 100;
            document.getElementById('progress').style.width = progress + '%';
        }

        function changeDifficulty(newDifficulty) {
            fillBlankDifficulty = newDifficulty;
            if (currentMode === 'fillblank') {
                showFillBlank();
            }
        }

        function handleBlankInput(event) {
            const input = event.target;
            const answer = input.dataset.answer;

            if (event.key === 'Enter') {
                checkSingleBlank(input);
            } else if (input.value.length === input.maxLength) {
                // 自动检查单字母输入
                if (input.maxLength === 1) {
                    setTimeout(() => checkSingleBlank(input), 100);
                }
            }
        }

        function checkSingleBlank(input) {
            const userAnswer = input.value.toLowerCase().trim();
            const correctAnswer = input.dataset.answer;

            if (userAnswer === correctAnswer) {
                input.classList.add('correct');
                input.disabled = true;
                playSuccessSound();

                // 检查是否所有空格都填对了
                const allInputs = document.querySelectorAll('.blank-input');
                const allCorrect = Array.from(allInputs).every(inp => inp.classList.contains('correct'));

                if (allCorrect) {
                    setTimeout(() => {
                        showFillBlankSuccess();
                    }, 500);
                }
            } else {
                input.classList.add('wrong');
                playErrorSound();
                setTimeout(() => {
                    input.classList.remove('wrong');
                    input.value = '';
                    input.focus();
                }, 1000);
            }
        }

        function checkFillBlank() {
            const inputs = document.querySelectorAll('.blank-input');
            let allCorrect = true;
            let correctCount = 0;

            inputs.forEach(input => {
                const userAnswer = input.value.toLowerCase().trim();
                const correctAnswer = input.dataset.answer;

                if (userAnswer === correctAnswer) {
                    input.classList.add('correct');
                    input.disabled = true;
                    correctCount++;
                } else {
                    input.classList.add('wrong');
                    allCorrect = false;
                    setTimeout(() => {
                        input.classList.remove('wrong');
                        if (!input.classList.contains('correct')) {
                            input.value = '';
                        }
                    }, 1500);
                }
            });

            if (allCorrect) {
                playEncouragementSound();
                showFillBlankSuccess();
            } else {
                playErrorSound();
                document.getElementById('fillBlankResult').innerHTML =
                    `<div style="color: #e74c3c; font-size: 1.2em;">还有${inputs.length - correctCount}个空格需要填写正确 💪</div>`;
            }
        }

        function showFillBlankSuccess() {
            const baseScore = fillBlankDifficulty === 'easy' ? 25 : fillBlankDifficulty === 'medium' ? 40 : 60;
            const hintPenalty = window.hintsUsed * 5;
            const finalScore = Math.max(10, baseScore - hintPenalty);

            score += finalScore;
            updateScore();

            const encouragements = [
                "🎉 太棒了！你真聪明！",
                "🌟 完美答对！继续加油！",
                "🏆 你是单词大师！",
                "✨ 答得非常好！",
                "🎯 百分百正确！厉害！"
            ];

            const randomEncouragement = encouragements[Math.floor(Math.random() * encouragements.length)];

            document.getElementById('fillBlankResult').innerHTML = `
                <div class="encouragement">${randomEncouragement}</div>
                <div style="color: #27ae60; font-size: 1.2em; margin: 10px 0;">
                    获得 ${finalScore} 分！${window.hintsUsed > 0 ? `(使用了${window.hintsUsed}次提示)` : ''}
                </div>
            `;

            setTimeout(() => {
                nextWord();
            }, 2500);
        }

        function showHint() {
            if (window.hintsUsed < window.currentHints.length) {
                const hint = window.currentHints[window.hintsUsed];
                window.hintsUsed++;

                const hintDiv = document.createElement('div');
                hintDiv.style.cssText = `
                    background: #fff3cd;
                    border: 1px solid #ffeaa7;
                    border-radius: 10px;
                    padding: 10px;
                    margin: 10px 0;
                    color: #856404;
                    font-size: 1em;
                `;
                hintDiv.innerHTML = `💡 提示 ${window.hintsUsed}: ${hint}`;

                document.getElementById('fillBlankResult').appendChild(hintDiv);

                // 播放提示音
                playTone(440, 0.2, 'sine');
            } else {
                document.getElementById('fillBlankResult').innerHTML +=
                    '<div style="color: #6c757d; margin: 10px 0;">没有更多提示了！相信你可以的！💪</div>';
            }
        }

        // 初始化游戏
        startGame();
    </script>
</body>
</html>
